import { Overlay, OverlayModule } from '@angular/cdk/overlay';
import { CommonModule } from '@angular/common';
import { Component, ElementRef, EventEmitter, HostListener, Input, Output } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { FilterItem } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/models';
import { FilterPipe } from '../../pipe';
import { BaseComponent } from '../base-component/base.component';

const DEPENDENCIES = {
  MODULES: [
    CommonModule,
    MatCheckboxModule,
    OverlayModule,
    MatIconModule,
    MatFormFieldModule,
    FormsModule,
    MatInputModule
  ],
  PIPES: [FilterPipe]
};

@Component({
  selector: 'app-multi-select',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.PIPES],
  templateUrl: './multi-select.component.html',
  styleUrl: './multi-select.component.scss'
})
export class MultiSelectComponent extends BaseComponent {
  @Input() filterDetail!: FilterItem;
  @Output() selectedFilterValues = new EventEmitter<void>();

  searchTerm!: string;

  constructor(
    private readonly elementRef: ElementRef,
    private readonly overlay: Overlay
  ) {
    super();
  }

  @HostListener('document:click', ['$event'])
  onBackdropClick(event: MouseEvent): void {
    if (!this.elementRef.nativeElement.contains(event.target)) {
      this.toggleFilter(false);
    }
  }

  onDropdownClick(event: MouseEvent): void {
    event.stopPropagation();
  }

  toggleFilter(isOpen: boolean): void {
    this.filterDetail.isOpen = isOpen;
    this.searchTerm = '';
  }

  isChecked(id: number): boolean {
    return this.filterDetail.value.has(id);
  }

  onCheckboxChange(event: { checked: boolean }, id: number): void {
    const { value } = this.filterDetail;

    event.checked ? value.add(id) : value.delete(id);
    this.updatePlaceholderAndSchedulerData();
  }

  setPlaceholder(): void {
    const selectedFilterValues = this.getSelectedValuesArray();
    const { totalCount, defaultPlaceholder } = this.filterDetail;

    this.filterDetail.placeholder =
      totalCount === selectedFilterValues.length
        ? defaultPlaceholder
        : this.getCustomPlaceholderName(selectedFilterValues);
  }

  getCustomPlaceholderName(selectedValues: Array<number>): string {
    if (!selectedValues.length) {
      return this.filterDetail.defaultPlaceholder;
    }

    const firstSelectedValueName = this.filterDetail.options.find((option) => option.id === selectedValues[0])?.name;
    const additionalCount = selectedValues.length > 1 ? `+${selectedValues.length - 1}` : '';
    return ` ${firstSelectedValueName} ${additionalCount}`;
  }

  getSelectedValuesArray(): Array<number> {
    return Array.from(this.filterDetail.value);
  }

  selectDeselectAllValues(event: any): void {
    this.filterDetail.value.clear();
    if (event.checked) {
      for (const option of this.filterDetail.options) {
        this.filterDetail.value.add(option.id);
      }
    }
    this.updatePlaceholderAndSchedulerData();
  }

  updatePlaceholderAndSchedulerData(): void {
    this.setPlaceholder();
    // Defer emit to the next microtask so parent reads the latest Set values
    Promise.resolve().then(() => this.selectedFilterValues.emit());
  }
}
